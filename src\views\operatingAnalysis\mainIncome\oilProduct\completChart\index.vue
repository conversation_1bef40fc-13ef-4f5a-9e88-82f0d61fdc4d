<template>
  <div class="complete-chart">
    <div class="chart-box" ref="chartBox"></div>
  </div>
</template>
<script>
import * as echarts from "echarts";
export default {
  name: "completeChart",
  data() {
    return {
      mychart: null
    };
  },
  mounted() {
    this.$nextTick(() => {
      this.initChart();
    });
    // 监听窗口大小变化，自动调整图表
    window.addEventListener('resize', this.handleResize);
  },
  beforeDestroy() {
    window.removeEventListener('resize', this.handleResize);
    if (this.mychart) {
      this.mychart.dispose();
    }
  },
  methods: {
    initChart() {
      this.mychart = echarts.init(this.$refs.chartBox);
      const option = {
        tooltip: {
          trigger: "axis",
          axisPointer: {
            type: "shadow",
          },
        },
        legend: {
            show: false
        },
       grid: {
         left: "3%",
          right: "4%",
          bottom: "8%", // 增加底部间距
          top: "5%",    // 增加顶部间距
          containLabel: true,
        },
        xAxis: {
          type: "value",
          axisLine: {
            show: true,
            lineStyle: {
              color: "rgba(172, 194, 226, 0.5)",
            },
          },
          axisTick: {
            show: true,
          },
          axisLabel: {
            color: "#ACC2E2",
          },
          splitLine: {
            lineStyle: {
              color: "rgba(172, 194, 226, 0.2)",
            },
          },
        },
        yAxis: {
          type: "category",
          data: ["陵水17-2", "崖城13-1", "陵水25-1", "文昌16-2", "崖城13-10"],
          axisLabel: {
            color: "#ACC2E2",
          },
          axisLine: {
            show: true,
            lineStyle: {
              color: "rgba(172, 194, 226, 0.5)",
            },
          },
          axisTick: {
            show: true,
          },
          boundaryGap: true,
        },
        series: [
          {
            name: "2011",
            type: "bar",
            data: [10, 45, 23, 88, 62],
            barCategoryGap: "60%",
          },
        ],
      };
      this.mychart.setOption(option);
    },
    handleResize() {
      if (this.mychart) {
        this.mychart.resize();
      }
    },
  },
};
</script>
<style lang="scss" scoped>
.complete-chart {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;

  .chart-box {
    width: 100%;
    flex: 1;
    min-height: 200px; // 减少最小高度，适应容器空间
    max-height: 280px; // 添加最大高度限制
  }
}
</style>

<template>
  <div class="processingFee">
    <div class="processingFee-title">
      <div class="processingFee-title-img"></div>
      <p>{{title}}</p>
    </div>
    <div class="processingFee-content">
      <div>
        <span class="price">{{content.fee}}</span>
        <span class="price-unit">元/方</span>
      </div>
      <div class="periodRatio" v-if="content.grow">
        <span>同期</span>
        <img src="@/assets/tableicon/rise-icon.png" alt="" />
        <span class="num">{{content.preCompare}}</span>
        <span>元/方</span>
      </div>
      <div class="periodRatio" v-else>
        <span>同期</span>
        <img src="@/assets/tableicon/decline-icon.png" alt="" />
        <span class="green-num">{{content.preCompare}}</span>
        <span>元/方</span>
      </div>
    </div>
  </div>
</template>
<script>
export default {
  name: "processingFeeItem",
  props: {
    title: {
      type: String,
    },
    content:{
      type:Object,
      default:()=>{}
    }
  },
};
</script>
<style lang="scss" scoped>
.processingFee {
  width: 100%;
  height: 192px;

  border-radius: 6px;
  background: #1a2e5e;
  box-sizing: border-box;
  border: 2px solid;
  border-image: linear-gradient(180deg, #1783ff 0%, rgba(23, 131, 255, 0) 100%)
    2;
  .processingFee-title {
    width: 100%;
    height: 64px;
    display: flex;
    align-items: center;
    justify-content: center;

    font-family: 思源黑体;
    font-size: 16px;
    .processingFee-title-img {
      width: 48px;
      height: 48px;
      margin-right: 12px;
    }
  }

  .processingFee-content {
    width: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    padding-top: 24px;
    font-size: 14px;
    .price {
      font-family: D-DIN;
      font-size: 32px;
      font-weight: bold;
      margin-right: 4px;
    }
    .price-unit {
      font-family: Source Han Sans;
      font-size: 14px;
      font-weight: normal;
    }
    .periodRatio {
      margin-top: 16px;
      img {
        width: 16px;
        height: 16px;
        margin: 0 4px;
      }
      .num {
        font-family: Source Han Sans;
        font-size: 14px;
        color: #FF6660;
        margin-right: 2px;
      }
      .green-num {
        font-family: Source Han Sans;
        font-size: 14px;
        color: #25CB97;
        margin-right: 2px;
      }
    }
  }
}

[data-theme="dark"] .processingFee-title {
  background: rgba(27, 126, 242, 0.16);
  color: #fff;
}
[data-theme="dark"] .processingFee-title-img {
  background-image: url("@/assets/tableicon/profee-darkicon.png");
  background-size: 100% 100%;
}
[data-theme="dark"] .processingFee-content {
  color: #fff;
  .price {
    background: linear-gradient(180deg, #ffffff 0%, #6bccff 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    color: transparent;
  }

  .price-unit {
    background: linear-gradient(180deg, #ffffff 0%, #6bccff 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    color: transparent;
  }
}
[data-theme="tint"] .processingFee-title {
  background: #ecf7ff;
  color: rgba(0, 0, 0, 0.85);
}
[data-theme="tint"] .processingFee-title-img {
  background-image: url("@/assets/tableicon/profee-tinticon.png");
   background-size: 100% 100%;
}
</style>

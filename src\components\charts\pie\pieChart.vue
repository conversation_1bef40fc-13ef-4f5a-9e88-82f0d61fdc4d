<template>
  <div class="chart-container">
    <div class="chart" ref="chart"></div>
  </div>
</template>
<script>
import * as echarts from "echarts";
import "echarts-gl";
import { getPie3D} from "./data";
export default {
  name: "chart",
  props: {
    chartData: {
      type: Array,
      default: () => [],
    },
    position: {
      type: Object,
      default: () => {},
    }
  },
  data() {
    return {
      optionData: [
        {
          name: "数据1",
          value: 36,
          itemStyle: { opacity: 1, color: "#4F42B9" },
        },
        {
          name: "数据2",
          value: 44,
          itemStyle: { opacity: 1, color: "#0DBD1F" },
        },
        {
          name: "数据3",
          value: 20,
          itemStyle: { opacity: 1, color: "#60FFEE" },
        },
        {
          name: "数据4",
          value: 20,
          itemStyle: { opacity: 1, color: "#FF8A00" },
        },
        {
          name: "数据5",
          value: 20,
          itemStyle: { opacity: 1, color: "#FF0091" },
        },
      ],
      statusChart: null,
      option: {},
    };
  },
  watch: {
    chartData: {
      handler(newVal, oldVal) {
        this.initChart();
      }
    }
  },
  mounted() {
    this.$nextTick(() => {
      this.initChart();
    });
  },
  methods: {
    initChart() {
      let mychart = echarts.init(this.$refs.chart);

      this.option = getPie3D(this.chartData, 0.6,this.position);
      mychart.setOption(this.option);
    },
  },
};
</script>
<style lang="scss" scoped>
.chart-container {
  width: 80%;
  height: 80%;
  flex: 1;
  .chart {
    z-index: 1;
  }
  .chart {
    width: 100%;
    height: 100%;
  }
}

canvas[data-zr-dom-is] {
  height: 320px !important;
}
</style>

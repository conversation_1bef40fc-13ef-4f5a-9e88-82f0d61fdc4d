<template>
  <div class="oilPrice">
  <div class="content-up">
      <div class="main-indicators">
        <chartBox :title="'主要指标总览'"></chartBox>
      </div>
      <div class="statistics-box">
        <chartBox :title="'分产品统计'"></chartBox>
      </div>
    </div>
    <div class="content-down">
      <div class="trend-box">
        <chartBox :title="'趋势分析'"></chartBox>
      </div>
      <div class="regional-distribution">
        <chartBox :title="'区域分布'"></chartBox>
      </div>
      <div class="sales-situation">
        <chartBox :title="'分区域销售情况'"></chartBox>
      </div>
    </div>
  </div>
</template>
<script>
export default {
  name: "oilPrice",
};
</script>
<style lang="scss" scoped>
.oilPrice {
  .content-up {
    display: flex;
    justify-content: space-between;
    .main-indicators {
      flex: 1;
      margin-right: 10px;
    }
    .statistics-box {
      flex: 1;
    }
  }

  .content-down {
    display: flex;
    justify-content: space-between;
    margin-top: 10px;
    .trend-box {
      flex: 1;
    }
    .regional-distribution {
      flex: 1;
      margin: 0 10px;
    }
    .sales-situation {
      flex: 1;
    }
  }
}
</style>

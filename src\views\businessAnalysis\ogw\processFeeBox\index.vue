<template>
  <div class="medicine-container">
    <div class="medicine-content">
      <ProcessingFee
        class="content-left"
        :title="'方油处理费'"
        :content="oilContent"
      ></ProcessingFee>
      <ProcessingFee
        class="content-right"
        :title="'方气处理费'"
        :content="gasContent"
      ></ProcessingFee>
    </div>
    <div class="bottom-bg"></div>
  </div>
</template>
<script>
import ProcessingFee from "../ProcessingFeeItem.vue";
import { getProcessFeeBox } from "@/api/ogw/index.js";
export default {
  name: "processFeeBox",
  components: {
    ProcessingFee,
  },
  props: {
    date: {
      type: String,
    },
  },
  watch: {
    date: {
      handler() {
        this.getData();
      }
    }
  },
  mounted() {
    this.getData();
  },
  data() {
    return {
      gasContent: {},
      oilContent: {},
    };
  },
  methods: {
    async getData() {
      const res = await getProcessFeeBox(this.date);
      if (res.code === 200) {
        this.gasContent = res.data.gas;
        this.oilContent = res.data.oil;
      }
    },
  },
};
</script>
<style lang="scss" scoped>
.medicine-container {
  width: 100%;
  display: flex;
  flex-direction: column;
  .medicine-content {
    display: flex;
    margin: 40px 32px 30px 32px;
    .content-left {
      flex: 1;
      margin-right: 17px;
    }

    .content-right {
      flex: 1;
    }
  }
  .bottom-bg {
    width: 100%;
    height: 89px;
    background-size: 100% 100%;
  }
}

[data-theme="dark"] .bottom-bg {
  background-image: url("@/assets/tableicon/base-darkbg.png");
}
</style>
